#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
KNOWLEDGE APP MAIN - Professional Solution Integrated

This version bypasses the problematic Enterprise DI Container for UI creation,
while maintaining the professional QtWebEngine + Local HTTP Server approach.
This resolves the startup deadlock and crash.
"""

import sys
import os
import logging
import signal
import threading
import time
from pathlib import Path

# 🔥 CRITICAL: Set QtWebEngine environment variables BEFORE importing PyQt6
# 🚨 VIRTUALIZATION-PROOF: Maximum compatibility for VirtualBox/VMware/Hyper-V
os.environ["QT_OPENGL"] = "software"
os.environ["QT_QUICK_BACKEND"] = "software"
os.environ["QTWEBENGINE_DISABLE_SANDBOX"] = "1"
os.environ["QTWEBENGINE_DISABLE_GPU_THREAD"] = "1"
os.environ["QTWEBENGINE_DISABLE_GPU"] = "1"
os.environ["QTWEBENGINE_DISABLE_GPU_COMPOSITING"] = "1"
os.environ["QTWEBENGINE_DISABLE_GPU_RASTERIZATION"] = "1"
os.environ["QTWEBENGINE_DISABLE_GPU_SANDBOX"] = "1"
os.environ["QTWEBENGINE_DISABLE_SHARED_WORKERS"] = "1"
os.environ["QTWEBENGINE_DISABLE_EXTENSIONS"] = "1"
os.environ["QTWEBENGINE_REMOTE_DEBUGGING_PORT"] = "0"
# 🚨 FORCE CPU-ONLY RENDERING - No GPU at all
# 🔧 FIX: Prevent QJsonValue undefined errors
os.environ["QT_LOGGING_RULES"] = "*.debug=false;qt.qpa.*=false"
os.environ["LIBGL_ALWAYS_SOFTWARE"] = "1"
os.environ["GALLIUM_DRIVER"] = "llvmpipe"
os.environ["MESA_GL_VERSION_OVERRIDE"] = "3.3"
os.environ["MESA_GLSL_VERSION_OVERRIDE"] = "330"
os.environ["QTWEBENGINE_CHROMIUM_FLAGS"] = "--disable-gpu --disable-gpu-compositing --disable-gpu-rasterization --disable-gpu-sandbox --no-sandbox --single-process --use-gl=disabled --disable-web-security --disable-features=VizDisplayCompositor --disable-accelerated-2d-canvas --disable-accelerated-jpeg-decoding --disable-accelerated-mjpeg-decode --disable-accelerated-video-decode --disable-accelerated-video-encode --disable-gpu-memory-buffer-video-frames --disable-webgl --disable-webgl2 --disable-3d-apis --disable-software-rasterizer --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-renderer-backgrounding --disable-features=TranslateUI --disable-ipc-flooding-protection"

# Fix Unicode encoding issues on Windows
if sys.platform == "win32":
    try:
        import subprocess
        subprocess.run(['chcp', '65001'], shell=True, capture_output=True)
        import codecs
        sys.stdout.reconfigure(encoding='utf-8', errors='replace')
        sys.stderr.reconfigure(encoding='utf-8', errors='replace')
    except Exception:
        pass  # Use ASCII fallback if UTF-8 setup fails

# --- Basic Setup ---
# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Configure clean logging - suppress debug spam and fix Unicode issues
import sys
import codecs

# Fix console encoding for Unicode characters
if sys.platform == 'win32':
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())

logging.basicConfig(
    level=logging.WARNING,  # Only show warnings and errors
    format='%(levelname)s: %(message)s',
    handlers=[
        logging.FileHandler('app.log', encoding='utf-8'),  # Log to file with UTF-8
        logging.StreamHandler()  # Keep minimal console output
    ]
)
logger = logging.getLogger(__name__)

# Suppress Qt debug output
import os
os.environ["QT_LOGGING_RULES"] = "*.debug=false"

# --- Import Core Qt Components ---
try:
    from PyQt6.QtCore import Qt, QCoreApplication, QTimer
    # 🔧 FIX: Set Qt attributes to prevent QJsonValue undefined errors
    QCoreApplication.setAttribute(Qt.ApplicationAttribute.AA_ShareOpenGLContexts, True)
    QCoreApplication.setAttribute(Qt.ApplicationAttribute.AA_UseSoftwareOpenGL, True)
    from PyQt6.QtWidgets import QApplication
    print("✅ Knowledge App starting...")
except ImportError as e:
    print(f"ERROR: PyQt6 not found. Please install it: pip install PyQt6 PyQt6-WebEngine")
    sys.exit(1)

try:
    from knowledge_app.utils.warning_manager import suppress_all_warnings
except Exception as e:
    logger.error(f"Import error: {e}")
    sys.exit(1)

# --- Global Shutdown Control ---
shutdown_initiated = False
exit_timeout_seconds = 300  # 5 minutes - app is working fine, just needs time


class KnowledgeAppTwoPhase:
    """
    🧠 TWO-PHASE KNOWLEDGE APP: Deadlock-free QtWebEngine initialization

    Phase 1 (Instant): Create QApplication and lightweight window shell
    Phase 2 (Deferred): Use QTimer.singleShot to schedule heavy initialization after event loop starts

    This prevents QtWebEngine subprocess deadlocks during startup while preserving all features.
    """

    def __init__(self, qt_app: QApplication):
        """Initialize with existing QApplication instance"""
        logger.info("🚀 Initializing KnowledgeAppTwoPhase...")

        self.qt_app = qt_app
        self.main_window = None
        self.qt_bridge = None

        # Core systems (will be initialized in Phase 2)
        self.di_container = None
        self.system_controller = None
        self.settings_service = None
        self.mcq_service = None
        self.history_service = None
        self.quiz_controller = None

        # Phase completion flags
        self._phase1_completed = False
        self._phase2_completed = False

    def initialize_phase1(self) -> bool:
        """
        Phase 1: Lightweight initialization - Create UI shell only

        This MUST be fast and non-blocking. No heavy operations allowed!
        """
        try:
            logger.info("🔥 Phase 1: Creating lightweight UI shell...")

            # Step 1: Create minimal Qt Bridge (no controllers yet)
            from knowledge_app.ui.qt_bridge import QtBridge
            self.qt_bridge = QtBridge(quiz_controller=None)
            logger.info("✅ Phase 1: Qt Bridge shell created")

            # Step 2: Create Main Window shell (no content loading)
            from knowledge_app.ui.main_window import MainApplicationWindow
            self.main_window = MainApplicationWindow(self.qt_bridge)
            logger.info("✅ Phase 1: Main window shell created")

            # Step 3: Setup window properties but don't load content
            self.main_window.setWindowTitle("Knowledge App - Loading...")

            self._phase1_completed = True
            logger.info("🎉 Phase 1 completed successfully - UI shell ready")
            return True

        except Exception as e:
            logger.error(f"❌ Phase 1 failed: {e}", exc_info=True)
            return False

    def show(self):
        """Show the main window"""
        if self.main_window:
            self.main_window.show()
            self.main_window.raise_()
            self.main_window.activateWindow()

    def run(self) -> int:
        """Run the application event loop"""
        return self.qt_app.exec()

    def initialize_heavy_services(self):
        """
        Phase 2: Heavy service initialization (called via QTimer.singleShot)

        This is where all the heavy lifting happens after the event loop starts.
        """
        try:
            logger.info("🔥 Phase 2: Starting heavy service initialization...")

            # Step 1: Initialize enterprise systems
            logger.info("🏢 Initializing enterprise systems...")
            self._initialize_enterprise_systems()

            # Step 2: Initialize core services
            logger.info("⚙️ Initializing core services...")
            if not self._initialize_services():
                logger.error("❌ Core services initialization failed")
                return

            # Step 3: Initialize controllers
            logger.info("🎮 Initializing controllers...")
            if not self._initialize_controllers():
                logger.error("❌ Controllers initialization failed")
                return

            # Step 4: Connect controllers to bridge
            logger.info("🌉 Connecting controllers to bridge...")
            self._connect_controllers_to_bridge()

            # Step 5: Load sophisticated UI content
            logger.info("🎨 Loading sophisticated UI content...")
            self._load_sophisticated_ui()

            # Step 6: Final system verification
            logger.info("🔍 Performing final system verification...")
            self._verify_system_health()

            self._phase2_completed = True
            logger.info("🎉 Phase 2 completed successfully - Full Knowledge App ready!")

        except Exception as e:
            logger.error(f"❌ Phase 2 failed: {e}", exc_info=True)
            # Continue with degraded functionality

    def _initialize_enterprise_systems(self):
        """Initialize enterprise-level systems"""
        try:
            # Initialize system controller
            from knowledge_app.controllers.system_controller import get_system_controller
            self.system_controller = get_system_controller()
            logger.info("✅ System controller initialized")

            # Initialize cache manager
            from knowledge_app.core.cache_manager import get_cache_manager
            cache_manager = get_cache_manager()
            logger.info("✅ Cache manager initialized")

            # Initialize UI response controller
            from knowledge_app.controllers.ui_response_controller import get_ui_response_controller
            ui_controller = get_ui_response_controller()
            ui_controller.initialize_cleanup_timer()
            logger.info("✅ UI response controller initialized")

        except Exception as e:
            logger.warning(f"⚠️ Enterprise systems initialization failed (non-critical): {e}")

    def _initialize_services(self) -> bool:
        """Initialize all business logic services"""
        try:
            # Initialize Settings Service
            from knowledge_app.services.settings_service import SettingsService
            self.settings_service = SettingsService()
            logger.info("✅ Settings Service initialized")

            # For now, create mock services to get UI working
            logger.info("🔧 Creating mock services for UI functionality...")
            self.mcq_service = MockMCQService()
            self.history_service = MockHistoryService()
            logger.info("✅ Mock services initialized")

            return True

        except Exception as e:
            logger.error(f"❌ Services initialization failed: {e}")
            return False



    def _initialize_controllers(self) -> bool:
        """Initialize all controllers"""
        try:
            # Initialize Quiz Controller with all dependencies
            from knowledge_app.controllers.quiz_controller import QuizController

            if not self.mcq_service or not self.history_service:
                logger.error("❌ Cannot initialize controllers - services not ready")
                return False

            self.quiz_controller = QuizController(
                mcq_service=self.mcq_service,
                history_service=self.history_service
            )

            logger.info("✅ Quiz Controller initialized")
            return True

        except Exception as e:
            logger.error(f"❌ Controllers initialization failed: {e}")
            return False

    def _connect_controllers_to_bridge(self):
        """Connect all controllers to the Qt Bridge"""
        try:
            if self.qt_bridge and self.quiz_controller:
                # Update bridge with the quiz controller
                self.qt_bridge.quiz_controller = self.quiz_controller
                logger.info("✅ Controllers connected to bridge")
            else:
                logger.warning("⚠️ Bridge or controllers not ready for connection")

        except Exception as e:
            logger.error(f"❌ Bridge connection failed: {e}")

    def _load_sophisticated_ui(self):
        """Load the sophisticated UI content with all features"""
        try:
            if self.main_window:
                # Update window title to show it's fully loaded
                self.main_window.setWindowTitle("Knowledge App - Professional Architecture")

                # Load the sophisticated UI via local web server
                self.main_window.load_ui()
                logger.info("✅ Sophisticated UI loading initiated")
            else:
                logger.error("❌ Main window not available for UI loading")

        except Exception as e:
            logger.error(f"❌ UI loading failed: {e}")

    def _verify_system_health(self):
        """Perform final system health verification"""
        try:
            health_checks = []

            # Check Phase 1 completion
            health_checks.append(("Phase 1", self._phase1_completed))

            # Check core components
            health_checks.append(("Main Window", self.main_window is not None))
            health_checks.append(("Qt Bridge", self.qt_bridge is not None))
            health_checks.append(("MCQ Service", self.mcq_service is not None))
            health_checks.append(("History Service", self.history_service is not None))
            health_checks.append(("Quiz Controller", self.quiz_controller is not None))

            # Report health status
            passed = sum(1 for _, status in health_checks if status)
            total = len(health_checks)

            logger.info(f"🏥 System Health: {passed}/{total} components healthy")

            for component, status in health_checks:
                status_icon = "✅" if status else "❌"
                logger.info(f"  {status_icon} {component}")

            if passed == total:
                logger.info("🎉 All systems operational - Knowledge App fully ready!")
            else:
                logger.warning(f"⚠️ {total - passed} components failed - running with degraded functionality")

        except Exception as e:
            logger.error(f"❌ System health check failed: {e}")


class MockMCQService:
    """Mock MCQ Service for UI testing"""
    def __init__(self):
        pass

    def generate_question(self, *args, **kwargs):
        return True

    def is_available(self):
        return True


class MockHistoryService:
    """Mock History Service for UI testing"""
    def __init__(self):
        pass

    def save_question(self, *args, **kwargs):
        return True

    def get_history(self, *args, **kwargs):
        return []

# --- 20 SECOND AUTO-CLOSE TIMER ---
auto_close_timer = None

def setup_auto_close_timer(app_instance):
    """Setup 5-minute auto-close timer to allow complete UI loading observation"""
    global auto_close_timer
    
    logger.info("🕐 Setting up 5-minute auto-close timer to observe complete UI loading")
    print("🕐 DEBUG: Setting up 5-minute auto-close timer")
    
    auto_close_timer = QTimer()
    auto_close_timer.setSingleShot(True)
    auto_close_timer.timeout.connect(lambda: force_auto_close(app_instance))
    auto_close_timer.start(300000)  # 5 minutes to allow full UI loading
    
    logger.info("⏰ 5-minute auto-close timer started - app will close automatically for log analysis")
    print("⏰ DEBUG: Auto-close timer active - app will close in 5 minutes")

def force_auto_close(app_instance):
    """Force close the app after 20 seconds for log analysis"""
    global shutdown_initiated
    
    logger.info("⏰ 20-SECOND TIMER EXPIRED - Auto-closing app for log analysis")
    print("⏰ DEBUG: 20-second timer expired - forcing app close for log analysis")
    
    shutdown_initiated = True
    
    if app_instance:
        app_instance.quit()
    
    logger.info("⏰ Auto-close completed - check logs for analysis")
    print("⏰ DEBUG: Auto-close completed")

def force_exit_handler():
    """Force exit after a timeout to prevent the app from hanging."""
    logger.info(f"Force exit handler started - will wait {exit_timeout_seconds}s for clean shutdown")
    print(f"DEBUG: Force exit handler waiting {exit_timeout_seconds}s...")
    
    time.sleep(exit_timeout_seconds)
    
    if not shutdown_initiated:
        logger.critical(f"FORCE EXIT: App did not exit cleanly within {exit_timeout_seconds}s. Terminating.")
        logger.warning("Application timeout reached - force terminating")
        os._exit(1)
    else:
        logger.info(f"Force exit handler completed - shutdown was initiated normally within {exit_timeout_seconds}s")

def signal_handler(sig, frame):
    """Handle Ctrl+C and other signals for a clean shutdown."""
    global shutdown_initiated, auto_close_timer
    if shutdown_initiated:
        logger.info(f"DUPLICATE SIGNAL {signal.Signals(sig).name} IGNORED - shutdown already in progress")
        return
    shutdown_initiated = True
    
    # Stop auto-close timer if signal received
    if auto_close_timer and auto_close_timer.isActive():
        auto_close_timer.stop()
        logger.info("⏰ Auto-close timer stopped due to manual signal")
    
    logger.info(f"SIGNAL {signal.Signals(sig).name} RECEIVED. Initiating clean shutdown...")
    logger.info(f"Signal received at frame: {frame.f_code.co_filename}:{frame.f_lineno}")
    print(f"DEBUG: Signal {signal.Signals(sig).name} received - starting shutdown sequence")
    QApplication.quit()
    logger.info(f"QApplication.quit() called in response to signal {signal.Signals(sig).name}")

def main():
    """
    Main entry point with a clean, professional, and deadlock-free architecture.
    """
    global shutdown_initiated
    
    # Track total execution time
    main_start_time = time.time()

    # 1. Setup graceful shutdown handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    threading.Thread(target=force_exit_handler, daemon=True).start()

    # 2. Perform lightweight, pre-Qt initializations
    try:
        # Suppress warnings for a cleaner console
        from knowledge_app.utils.warning_manager import suppress_all_warnings
        suppress_all_warnings()

        # Setup automatic log cleanup
        from knowledge_app.utils.log_cleanup_manager import setup_automatic_log_cleanup, DEFAULT_KNOWLEDGE_APP_CONFIG
        logs_dir = Path(__file__).parent / "logs"
        setup_automatic_log_cleanup(logs_dir, DEFAULT_KNOWLEDGE_APP_CONFIG)

    except Exception as e:
        print(f"ERROR: Pre-Qt initialization failed: {e}")
        return 1
    # 3. CRITICAL: Create stable QApplication with QtWebEngine crash-proofing

    # Set additional attributes for QtWebEngine compatibility
    try:
        QApplication.setAttribute(Qt.ApplicationAttribute.AA_DisableWindowContextHelpButton, True)
    except AttributeError:
        pass  # Not available in all PyQt6 versions

    # 🚨 VIRTUALIZATION-PROOF: Maximum aggressive software rendering for VirtualBox
    try:
        QApplication.setAttribute(Qt.ApplicationAttribute.AA_UseSoftwareOpenGL, True)
    except AttributeError:
        pass

    # Disable ALL hardware acceleration attributes
    try:
        QApplication.setAttribute(Qt.ApplicationAttribute.AA_UseDesktopOpenGL, False)
    except AttributeError:
        pass

    try:
        QApplication.setAttribute(Qt.ApplicationAttribute.AA_UseOpenGLES, False)
    except AttributeError:
        pass

    # Additional virtualization-safe attributes
    try:
        QApplication.setAttribute(Qt.ApplicationAttribute.AA_DisableShaderDiskCache, True)
    except AttributeError:
        pass

    try:
        QApplication.setAttribute(Qt.ApplicationAttribute.AA_CompressHighFrequencyEvents, True)
    except AttributeError:
        pass

    # Create QApplication with crash-proof settings
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
        app.setApplicationName("Knowledge App")
        app.setApplicationVersion("2.1.0-fixed")
        app.setOrganizationName("Knowledge Systems")
        app.setQuitOnLastWindowClosed(False)  # Prevent auto-quit

    # 🕐 DISABLED: Auto-close timer - app is working successfully now
    # setup_auto_close_timer(app)

    # 4. Create the application with integrated two-phase initialization
    print("🚀 Creating Knowledge App with integrated two-phase initialization...")
    knowledge_app = None
    try:
        # Create the two-phase Knowledge App directly in main.py
        knowledge_app = KnowledgeAppTwoPhase(app)
        print("✅ Knowledge App instance created successfully!")
    except Exception as e:
        print(f"ERROR: Failed to create the application: {e}")
        shutdown_initiated = True
        return 1

    # 5. Execute Phase 1: Create lightweight UI shell
    print("🔥 Phase 1: Creating UI shell...")
    try:
        if not knowledge_app.initialize_phase1():
            print("ERROR: Phase 1 initialization failed")
            return 1
        print("✅ Phase 1 completed - UI shell ready")
    except Exception as e:
        print(f"ERROR: Phase 1 failed: {e}")
        return 1

    # 6. Show the window immediately (Phase 1 complete)
    print("🎯 Showing main window...")
    try:
        knowledge_app.show()
        print("✅ Main window displayed")
    except Exception as e:
        print(f"ERROR: Failed to show main window: {e}")
        return 1

    # 7. Schedule Phase 2: Heavy service initialization (deferred)
    print("⏰ Scheduling Phase 2 initialization...")
    try:
        # Use QTimer.singleShot to defer heavy initialization until after event loop starts
        QTimer.singleShot(100, knowledge_app.initialize_heavy_services)
        print("✅ Phase 2 scheduled successfully")
    except Exception as e:
        print(f"ERROR: Failed to schedule Phase 2: {e}")
        return 1

    # 8. Start the application's event loop
    print("🎯 Starting Knowledge App event loop...")
    try:
        exit_code = knowledge_app.run()


    except KeyboardInterrupt:
        exit_code = 130

    except SystemExit as sys_exit:
        exit_code = sys_exit.code if sys_exit.code is not None else 1

    except Exception as e:
        print(f"ERROR: Exception during application run: {e}")
        exit_code = 1
    
    # Clean shutdown
    if exit_code == 0:
        print("✅ Knowledge App closed normally")
    elif exit_code == 130:
        print("⚠️ Knowledge App interrupted by user")
    else:
        print(f"⚠️ Knowledge App exited with code: {exit_code}")
    
    return exit_code

if __name__ == "__main__":
    try:
        # Run the main application
        final_exit_code = main()
        sys.exit(final_exit_code)

    except KeyboardInterrupt:
        print("⚠️ Interrupted by user")
        sys.exit(130)  # Standard exit code for Ctrl+C

    except SystemExit as e:
        raise  # Re-raise SystemExit

    except Exception as e:
        print(f"FATAL ERROR: {e}")
        sys.exit(1)